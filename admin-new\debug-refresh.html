<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台刷新问题诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #389e0d; }
        .status.warning { background: #fffbe6; border: 1px solid #ffe58f; color: #d48806; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #cf1322; }
        .status.info { background: #f0f9ff; border: 1px solid #91d5ff; color: #1890ff; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #40a9ff; }
        .log {
            background: #f8f8f8;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .counter {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <h1>🔍 管理后台自动刷新问题诊断</h1>
    
    <div class="card">
        <h2>📊 页面状态监控</h2>
        <div class="status info">
            页面加载时间: <span id="loadTime"></span>
        </div>
        <div class="status info">
            刷新次数: <span id="refreshCount" class="counter">0</span>
        </div>
        <div class="status info">
            运行时长: <span id="uptime">0秒</span>
        </div>
    </div>

    <div class="card">
        <h2>🔄 定时器监控</h2>
        <div id="timers"></div>
        <button onclick="checkTimers()">检查定时器</button>
        <button onclick="clearAllTimers()">清除所有定时器</button>
    </div>

    <div class="card">
        <h2>🚨 错误监控</h2>
        <div id="errors"></div>
        <button onclick="clearErrors()">清除错误日志</button>
    </div>

    <div class="card">
        <h2>🌐 网络请求监控</h2>
        <div id="requests"></div>
        <button onclick="clearRequests()">清除请求日志</button>
    </div>

    <div class="card">
        <h2>🛠️ 快速修复</h2>
        <button onclick="disableHMR()">禁用HMR</button>
        <button onclick="clearLocalStorage()">清除本地存储</button>
        <button onclick="disableAutoRefresh()">禁用自动刷新</button>
        <button onclick="restartApp()">重启应用</button>
    </div>

    <div class="card">
        <h2>📝 实时日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let refreshCount = parseInt(localStorage.getItem('refreshCount') || '0');
        let startTime = Date.now();
        let errors = [];
        let requests = [];
        let timers = [];

        // 更新刷新计数
        refreshCount++;
        localStorage.setItem('refreshCount', refreshCount.toString());
        document.getElementById('refreshCount').textContent = refreshCount;

        // 记录页面加载时间
        document.getElementById('loadTime').textContent = new Date().toLocaleString();

        // 更新运行时长
        setInterval(() => {
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('uptime').textContent = uptime + '秒';
        }, 1000);

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        // 监控错误
        window.addEventListener('error', (e) => {
            const error = {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                timestamp: new Date().toLocaleString()
            };
            errors.push(error);
            updateErrorDisplay();
            log(`错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });

        // 监控未捕获的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            const error = {
                reason: e.reason,
                timestamp: new Date().toLocaleString()
            };
            errors.push(error);
            updateErrorDisplay();
            log(`Promise拒绝: ${e.reason}`, 'error');
        });

        // 监控网络请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            const startTime = Date.now();
            log(`请求开始: ${url}`, 'info');
            
            return originalFetch.apply(this, args)
                .then(response => {
                    const duration = Date.now() - startTime;
                    requests.push({
                        url,
                        status: response.status,
                        duration,
                        timestamp: new Date().toLocaleString()
                    });
                    updateRequestDisplay();
                    log(`请求完成: ${url} (${response.status}, ${duration}ms)`, 'info');
                    return response;
                })
                .catch(error => {
                    const duration = Date.now() - startTime;
                    requests.push({
                        url,
                        error: error.message,
                        duration,
                        timestamp: new Date().toLocaleString()
                    });
                    updateRequestDisplay();
                    log(`请求失败: ${url} (${error.message}, ${duration}ms)`, 'error');
                    throw error;
                });
        };

        // 检查定时器
        function checkTimers() {
            // 检查setInterval
            const intervals = [];
            for (let i = 1; i < 10000; i++) {
                try {
                    if (clearInterval(i) === undefined) {
                        intervals.push(i);
                    }
                } catch (e) {}
            }
            
            // 检查setTimeout
            const timeouts = [];
            for (let i = 1; i < 10000; i++) {
                try {
                    if (clearTimeout(i) === undefined) {
                        timeouts.push(i);
                    }
                } catch (e) {}
            }

            timers = [...intervals.map(id => ({type: 'interval', id})), ...timeouts.map(id => ({type: 'timeout', id}))];
            updateTimerDisplay();
            log(`发现 ${intervals.length} 个setInterval, ${timeouts.length} 个setTimeout`, 'warning');
        }

        function updateErrorDisplay() {
            const errorDiv = document.getElementById('errors');
            if (errors.length === 0) {
                errorDiv.innerHTML = '<div class="status success">暂无错误</div>';
            } else {
                errorDiv.innerHTML = errors.slice(-5).map(error => 
                    `<div class="status error">${error.timestamp}: ${error.message || error.reason}</div>`
                ).join('');
            }
        }

        function updateRequestDisplay() {
            const requestDiv = document.getElementById('requests');
            if (requests.length === 0) {
                requestDiv.innerHTML = '<div class="status success">暂无请求</div>';
            } else {
                requestDiv.innerHTML = requests.slice(-5).map(req => 
                    `<div class="status ${req.error ? 'error' : 'success'}">${req.timestamp}: ${req.url} ${req.status || req.error}</div>`
                ).join('');
            }
        }

        function updateTimerDisplay() {
            const timerDiv = document.getElementById('timers');
            if (timers.length === 0) {
                timerDiv.innerHTML = '<div class="status success">暂无活跃定时器</div>';
            } else {
                timerDiv.innerHTML = `<div class="status warning">发现 ${timers.length} 个活跃定时器</div>`;
            }
        }

        // 修复功能
        function disableHMR() {
            log('尝试禁用HMR...', 'warning');
            // 这个需要在Vite配置中修改
            alert('请在vite.config.ts中设置 hmr: false');
        }

        function clearLocalStorage() {
            localStorage.clear();
            sessionStorage.clear();
            log('已清除所有本地存储', 'warning');
        }

        function disableAutoRefresh() {
            // 尝试清除所有定时器
            for (let i = 1; i < 10000; i++) {
                clearInterval(i);
                clearTimeout(i);
            }
            log('已尝试清除所有定时器', 'warning');
        }

        function restartApp() {
            localStorage.setItem('refreshCount', '0');
            window.location.reload();
        }

        function clearErrors() {
            errors = [];
            updateErrorDisplay();
        }

        function clearRequests() {
            requests = [];
            updateRequestDisplay();
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function clearAllTimers() {
            disableAutoRefresh();
            checkTimers();
        }

        // 初始化显示
        updateErrorDisplay();
        updateRequestDisplay();
        updateTimerDisplay();
        checkTimers();

        log('诊断工具已启动', 'info');
        log(`这是第 ${refreshCount} 次页面加载`, 'warning');

        // 监控页面卸载
        window.addEventListener('beforeunload', () => {
            log('页面即将卸载/刷新', 'warning');
        });
    </script>
</body>
</html>
