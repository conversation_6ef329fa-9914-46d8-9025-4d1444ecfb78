import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { getAccessToken, useAuthStore } from '@/stores/authStore'
import { env } from '@/utils/env'
import type { ApiResponse } from '@/types'

// API基础URL
const BASE_URL = env.API_BASE_URL

// 创建axios实例
export const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: env.REQUEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证Token
    const token = getAccessToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应格式
    const data = response.data
    
    // 如果返回的不是标准格式，包装一下
    if (!('success' in data)) {
      return {
        ...response,
        data: {
          success: true,
          data: data,
          message: 'success',
          code: response.status,
          timestamp: Date.now()
        } as ApiResponse
      }
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 401错误处理 - Token过期或无效
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 尝试刷新Token
        const { refreshToken } = useAuthStore.getState()
        const success = await refreshToken()
        
        if (success) {
          // 重新发送原请求
          const newToken = getAccessToken()
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return apiClient(originalRequest)
        } else {
          // 刷新失败，标记需要重新登录
          useAuthStore.getState().logout()
          console.warn('🚨 Token刷新失败，需要重新登录')
        }
      } catch (refreshError) {
        // 刷新Token失败，标记需要重新登录
        useAuthStore.getState().logout()
        console.warn('🚨 Token刷新异常，需要重新登录')
        return Promise.reject(refreshError)
      }
    }

    // 网络错误处理
    if (!error.response) {
      return Promise.reject({
        success: false,
        message: '网络连接错误，请检查网络设置',
        code: 0,
        data: null,
        timestamp: Date.now()
      } as ApiResponse)
    }

    // 其他HTTP错误处理
    const errorResponse: ApiResponse = {
      success: false,
      message: getErrorMessage(error.response.status, error.response.data),
      code: error.response.status,
      data: error.response.data,
      timestamp: Date.now()
    }

    return Promise.reject(errorResponse)
  }
)

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 获取错误信息
const getErrorMessage = (status: number, data: any): string => {
  // 优先使用服务器返回的错误信息
  if (data?.message) {
    return data.message
  }

  // 根据状态码返回默认错误信息
  const errorMessages: Record<number, string> = {
    400: '请求参数错误',
    401: '用户未登录或登录已过期',
    403: '没有权限访问该资源',
    404: '请求的资源不存在',
    405: '请求方法不被允许',
    408: '请求超时',
    409: '资源冲突',
    422: '请求参数验证失败',
    429: '请求过于频繁，请稍后再试',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂时不可用',
    504: '网关超时'
  }

  return errorMessages[status] || `请求失败 (${status})`
}

// 请求重试机制
export const createRetryableRequest = <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  return new Promise((resolve, reject) => {
    let attempts = 0

    const attemptRequest = async () => {
      try {
        const result = await requestFn()
        resolve(result)
      } catch (error) {
        attempts++
        
        if (attempts >= maxRetries) {
          reject(error)
          return
        }

        // 指数退避延迟
        const retryDelay = delay * Math.pow(2, attempts - 1)
        
        setTimeout(() => {
          attemptRequest()
        }, retryDelay)
      }
    }

    attemptRequest()
  })
}

// 并发请求控制
class ConcurrencyManager {
  private queue: Array<() => Promise<any>> = []
  private running: number = 0
  private maxConcurrency: number

  constructor(maxConcurrency: number = 5) {
    this.maxConcurrency = maxConcurrency
  }

  async add<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          this.running++
          const result = await requestFn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.running--
          this.processQueue()
        }
      })

      this.processQueue()
    })
  }

  private processQueue() {
    if (this.running >= this.maxConcurrency || this.queue.length === 0) {
      return
    }

    const nextRequest = this.queue.shift()
    if (nextRequest) {
      nextRequest()
    }
  }
}

export const concurrencyManager = new ConcurrencyManager()

// 请求缓存管理
class RequestCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key: string): any | null {
    const cached = this.cache.get(key)
    
    if (!cached) {
      return null
    }

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  clear() {
    this.cache.clear()
  }

  delete(key: string) {
    this.cache.delete(key)
  }
}

export const requestCache = new RequestCache()

// 创建带缓存的请求函数
export const createCachedRequest = <T>(
  key: string,
  requestFn: () => Promise<T>,
  ttl?: number
): Promise<T> => {
  const cached = requestCache.get(key)
  
  if (cached) {
    return Promise.resolve(cached)
  }

  return requestFn().then(result => {
    requestCache.set(key, result, ttl)
    return result
  })
}

// 批量请求工具
export const batchRequests = async <T>(
  requests: Array<() => Promise<T>>,
  batchSize: number = 10
): Promise<T[]> => {
  const results: T[] = []
  
  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map(req => req()))
    results.push(...batchResults)
  }
  
  return results
}

// 上传文件的专用实例
export const uploadClient = axios.create({
  baseURL: BASE_URL,
  timeout: 120000, // 2分钟超时
  headers: {
    'Content-Type': 'multipart/form-data',
  },
})

// 为上传客户端添加认证
uploadClient.interceptors.request.use((config) => {
  const token = getAccessToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

export default apiClient