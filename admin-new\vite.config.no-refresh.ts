import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      // 完全禁用快速刷新
      fastRefresh: false,
      babel: {
        presets: [],
        plugins: []
      }
    })
  ],

  // 统一的路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 完全禁用自动刷新的开发服务器配置
  server: {
    port: 8080,
    host: '0.0.0.0',
    strictPort: false,
    cors: true,
    // 🔥 完全禁用HMR避免自动刷新
    hmr: false,
    watch: {
      usePolling: false,
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**']
    },
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        format: 'es',
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['antd', '@ant-design/icons'],
          'utils-vendor': ['dayjs', 'lodash-es', 'axios']
        }
      }
    }
  },

  // ESBuild配置
  esbuild: {
    target: 'es2020',
    format: 'esm',
    keepNames: true
  },

  // 全局变量定义
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  },

  // 依赖优化
  optimizeDeps: {
    include: [
      'react', 
      'react-dom', 
      'antd', 
      '@ant-design/icons',
      'dayjs', 
      'lodash-es',
      'axios',
      'react-router-dom'
    ],
    esbuildOptions: {
      target: 'es2020'
    }
  }
})
