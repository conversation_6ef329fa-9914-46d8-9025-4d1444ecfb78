/**
 * 管理后台实时数据服务
 * 基于云函数调用实现免费的数据实时同步
 * 
 * 核心功能：
 * 1. 与dataSyncBridge云函数通信
 * 2. 实时数据获取和更新
 * 3. 自动刷新机制
 * 4. 缓存管理
 */

import cloudFunctionService from './cloudFunctionService'

interface DataResponse<T = any> {
  success: boolean
  data: T
  timestamp: number
  fromCache?: boolean
  fallback?: boolean
}

interface DataItem {
  _id: string
  [key: string]: any
}

interface UpdateOperation {
  collection: string
  operation: 'create' | 'update' | 'delete'
  docId?: string
  updateData?: any
}

interface DashboardStats {
  totalUsers: number
  totalStudents: number
  totalComments: number
  totalRecords: number
  todayComments: number
  lastUpdated: string
}

interface RealtimeData {
  recentActivities: any[]
  activeUsers: number
  serverTime: number
}

class RealTimeDataService {
  private isInitialized = false
  private autoRefreshInterval: NodeJS.Timeout | null = null
  private refreshFrequency = 30000 // 30秒刷新一次
  private localCache = new Map<string, any>()
  private eventListeners = new Map<string, Function[]>()
  private lastSyncTime = 0

  constructor() {
    console.log('🔄 RealTimeDataService 初始化')
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      console.log('🚀 初始化实时数据服务...')
      
      // 健康检查
      await this.healthCheck()
      
      // 恢复本地缓存
      this.restoreLocalCache()
      
      // 启动自动刷新
      this.startAutoRefresh()
      
      this.isInitialized = true
      console.log('✅ 实时数据服务初始化完成')
      
    } catch (error) {
      console.error('❌ 实时数据服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取数据 - 统一数据获取接口
   */
  async getData<T = DataItem[]>(
    collection: string, 
    filters: Record<string, any> = {}, 
    forceRefresh = false
  ): Promise<DataResponse<T[]>> {
    try {
      const cacheKey = this.getCacheKey(collection, filters)
      
      // 如果不强制刷新且有缓存，先返回缓存数据
      if (!forceRefresh && this.localCache.has(cacheKey)) {
        const cached = this.localCache.get(cacheKey)
        console.log(`📦 从缓存获取数据: ${collection}`)
        
        // 异步更新缓存
        this.refreshDataInBackground(collection, filters)
        
        return {
          success: true,
          data: cached.data,
          timestamp: cached.timestamp,
          fromCache: true
        }
      }
      
      // 从云端获取数据
      return await this.fetchDataFromCloud<T>(collection, filters)
      
    } catch (error) {
      console.error(`❌ 获取数据失败: ${collection}`, error)
      
      // 降级到缓存数据
      const cacheKey = this.getCacheKey(collection, filters)
      if (this.localCache.has(cacheKey)) {
        const cached = this.localCache.get(cacheKey)
        console.log(`🔄 降级使用缓存数据: ${collection}`)
        return {
          success: true,
          data: cached.data,
          timestamp: cached.timestamp,
          fromCache: true,
          fallback: true
        }
      }
      
      throw error
    }
  }

  /**
   * 更新数据
   */
  async updateData(operation: UpdateOperation): Promise<DataResponse> {
    try {
      console.log(`✏️ 更新数据: ${operation.collection}.${operation.operation}`)
      
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'updateData',
        source: 'admin',
        data: operation
      })

      if (result.code === 0 && result.data) {
        // 更新本地缓存
        this.updateLocalCache(operation.collection, operation.operation, operation.docId, operation.updateData)
        
        // 触发数据变更事件
        this.emitDataChange(operation.collection, operation.operation, operation.docId, operation.updateData)
        
        console.log(`✅ 数据更新成功: ${operation.collection}.${operation.operation}`)
        return {
          success: true,
          data: result.data,
          timestamp: Date.now()
        }
      } else {
        throw new Error(result.message || '数据更新失败')
      }
      
    } catch (error) {
      console.error(`❌ 数据更新失败: ${operation.collection}.${operation.operation}`, error)
      throw error
    }
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats(forceRefresh = false): Promise<DataResponse<DashboardStats>> {
    try {
      const cacheKey = 'dashboard_stats'
      
      if (!forceRefresh && this.localCache.has(cacheKey)) {
        const cached = this.localCache.get(cacheKey)
        console.log('📦 从缓存获取仪表板统计')
        
        // 异步更新缓存
        this.refreshDashboardStatsInBackground()
        
        return {
          success: true,
          data: cached.data,
          timestamp: cached.timestamp,
          fromCache: true
        }
      }
      
      console.log('📊 获取仪表板统计数据')
      
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'getDashboardStats',
        source: 'admin'
      })

      if (result.code === 0 && result.data) {
        // 更新缓存
        this.localCache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now()
        })
        this.persistCache()
        
        return {
          success: true,
          data: result.data,
          timestamp: Date.now()
        }
      } else {
        throw new Error(result.message || '获取统计数据失败')
      }
      
    } catch (error) {
      console.error('❌ 获取仪表板统计失败:', error)
      throw error
    }
  }

  /**
   * 获取实时数据
   */
  async getRealtimeData(): Promise<DataResponse<RealtimeData>> {
    try {
      console.log('⚡ 获取实时数据')
      
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'getRealtimeData',
        source: 'admin'
      })

      if (result.code === 0 && result.data) {
        return {
          success: true,
          data: result.data,
          timestamp: Date.now()
        }
      } else {
        throw new Error(result.message || '获取实时数据失败')
      }
      
    } catch (error) {
      console.error('❌ 获取实时数据失败:', error)
      throw error
    }
  }

  /**
   * 从云端获取数据
   */
  private async fetchDataFromCloud<T = DataItem>(
    collection: string, 
    filters: Record<string, any> = {}
  ): Promise<DataResponse<T[]>> {
    try {
      console.log(`☁️ 从云端获取数据: ${collection}`)
      
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'getData',
        source: 'admin',
        collection,
        filters
      })

      if (result.code === 0 && result.data) {
        const data = result.data.items || []
        
        // 更新本地缓存
        const cacheKey = this.getCacheKey(collection, filters)
        this.localCache.set(cacheKey, {
          data,
          timestamp: Date.now(),
          collection,
          filters
        })
        
        // 持久化缓存
        this.persistCache()
        
        console.log(`✅ 云端数据获取成功: ${collection}, ${data.length} 条记录`)
        
        return {
          success: true,
          data,
          timestamp: Date.now()
        }
      } else {
        throw new Error(result.message || '获取数据失败')
      }
      
    } catch (error) {
      console.error(`❌ 云端数据获取失败: ${collection}`, error)
      throw error
    }
  }

  /**
   * 后台刷新数据
   */
  private async refreshDataInBackground(collection: string, filters: Record<string, any> = {}): Promise<void> {
    try {
      await this.fetchDataFromCloud(collection, filters)
    } catch (error) {
      console.warn(`⚠️ 后台数据刷新失败: ${collection}`, error)
    }
  }

  /**
   * 后台刷新仪表板统计
   */
  private async refreshDashboardStatsInBackground(): Promise<void> {
    try {
      await this.getDashboardStats(true)
    } catch (error) {
      console.warn('⚠️ 后台仪表板统计刷新失败:', error)
    }
  }

  /**
   * 启动自动刷新
   */
  startAutoRefresh(): void {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval)
    }

    this.autoRefreshInterval = setInterval(async () => {
      try {
        await this.checkForUpdates()
      } catch (error) {
        console.warn('⚠️ 自动刷新检查失败:', error)
      }
    }, this.refreshFrequency)

    console.log(`🔄 自动刷新已启动，间隔: ${this.refreshFrequency}ms`)
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh(): void {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval)
      this.autoRefreshInterval = null
      console.log('⏹️ 自动刷新已停止')
    }
  }

  /**
   * 检查更新
   */
  private async checkForUpdates(): Promise<void> {
    try {
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'getUpdates',
        source: 'admin',
        data: {
          since: this.lastSyncTime
        }
      })

      if (result.code === 0 && result.data) {
        const { hasChanges, changes, lastSyncTime } = result.data
        
        if (hasChanges && changes.length > 0) {
          console.log(`🔄 发现 ${changes.length} 个数据更新`)
          
          // 处理数据变更
          this.processDataChanges(changes)
          
          // 更新同步时间
          this.lastSyncTime = lastSyncTime
          localStorage.setItem('lastSyncTime', this.lastSyncTime.toString())
        }
      }
      
    } catch (error) {
      console.warn('⚠️ 检查更新失败:', error)
    }
  }

  /**
   * 处理数据变更
   */
  private processDataChanges(changes: any[]): void {
    for (const change of changes) {
      try {
        const { collection, operation, docId } = change
        
        // 清除相关缓存
        this.clearCacheByCollection(collection)
        
        // 触发数据变更事件
        this.emitDataChange(collection, operation, docId, change.data)
        
      } catch (error) {
        console.warn('⚠️ 处理数据变更失败:', error)
      }
    }
  }

  /**
   * 健康检查
   */
  private async healthCheck(): Promise<void> {
    try {
      const result = await cloudFunctionService.callFunction('dataSyncBridge', {
        action: 'healthCheck',
        source: 'admin'
      })

      if (result.code !== 0) {
        throw new Error('健康检查失败')
      }
      
      console.log('✅ 数据服务健康检查通过')
    } catch (error) {
      console.error('❌ 数据服务健康检查失败:', error)
      throw error
    }
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(collection: string, filters: Record<string, any>): string {
    return `${collection}_${JSON.stringify(filters)}`
  }

  /**
   * 清除指定集合的缓存
   */
  private clearCacheByCollection(collection: string): void {
    const keysToDelete: string[] = []
    for (const [key, value] of this.localCache.entries()) {
      if (value.collection === collection) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => {
      this.localCache.delete(key)
    })
    
    console.log(`🗑️ 清除缓存: ${collection}, ${keysToDelete.length} 个键`)
  }

  /**
   * 恢复本地缓存
   */
  private restoreLocalCache(): void {
    try {
      const cachedData = localStorage.getItem('realTimeDataCache')
      const lastSyncTime = localStorage.getItem('lastSyncTime')
      
      if (cachedData) {
        this.localCache = new Map(JSON.parse(cachedData))
        console.log(`📦 恢复本地缓存: ${this.localCache.size} 个项目`)
      }
      
      if (lastSyncTime) {
        this.lastSyncTime = parseInt(lastSyncTime)
        console.log(`⏰ 恢复同步时间: ${new Date(this.lastSyncTime).toLocaleString()}`)
      }
      
    } catch (error) {
      console.warn('⚠️ 恢复本地缓存失败:', error)
    }
  }

  /**
   * 持久化缓存
   */
  private persistCache(): void {
    try {
      const cacheData = JSON.stringify([...this.localCache.entries()])
      localStorage.setItem('realTimeDataCache', cacheData)
    } catch (error) {
      console.warn('⚠️ 持久化缓存失败:', error)
    }
  }

  /**
   * 更新本地缓存
   */
  private updateLocalCache(collection: string, operation: string, docId?: string, updateData?: any): void {
    // 清除相关缓存，强制下次重新获取
    this.clearCacheByCollection(collection)
    this.persistCache()
  }

  /**
   * 添加数据变更监听器
   */
  addEventListener(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * 移除数据变更监听器
   */
  removeEventListener(event: string, callback: Function): void {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)!
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发数据变更事件
   */
  private emitDataChange(collection: string, operation: string, docId?: string, data?: any): void {
    const event = `dataChange:${collection}`
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event)!.forEach(callback => {
        try {
          callback({ collection, operation, docId, data })
        } catch (error) {
          console.warn('⚠️ 事件回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      cacheSize: this.localCache.size,
      lastSyncTime: this.lastSyncTime,
      refreshFrequency: this.refreshFrequency,
      isAutoRefreshRunning: !!this.autoRefreshInterval
    }
  }

  /**
   * 🔍 调试缓存状态
   */
  debugCacheStatus(): {
    cacheSize: number
    cacheKeys: string[]
    dashboardCached: boolean
    lastSyncTime: number
    cacheData: any[]
  } {
    const cacheKeys = Array.from(this.localCache.keys())
    const cacheData = Array.from(this.localCache.entries()).map(([key, value]) => ({
      key,
      timestamp: value.timestamp,
      dataSize: JSON.stringify(value.data).length,
      collection: value.collection || 'unknown'
    }))

    return {
      cacheSize: this.localCache.size,
      cacheKeys,
      dashboardCached: this.localCache.has('dashboard_stats'),
      lastSyncTime: this.lastSyncTime,
      cacheData
    }
  }

  /**
   * 🧹 清理所有缓存
   */
  clearAllCache(): {
    success: boolean
    clearedCount: number
    errors: string[]
  } {
    const result = {
      success: true,
      clearedCount: 0,
      errors: [] as string[]
    }

    try {
      result.clearedCount = this.localCache.size
      this.localCache.clear()

      // 清理localStorage中的缓存
      try {
        localStorage.removeItem('realTimeDataCache')
        localStorage.removeItem('lastSyncTime')
      } catch (error) {
        result.errors.push(`清理localStorage失败: ${error.message}`)
      }

      this.lastSyncTime = 0

      console.log(`🧹 已清理所有缓存，共 ${result.clearedCount} 个项目`)

    } catch (error) {
      result.success = false
      result.errors.push(`清理缓存失败: ${error.message}`)
      console.error('清理缓存失败:', error)
    }

    return result
  }

  /**
   * 🔄 强制刷新仪表板数据（跳过缓存）
   */
  async forceRefreshDashboard(): Promise<DataResponse<DashboardStats>> {
    console.log('🔄 强制刷新仪表板数据（跳过缓存）')

    // 先清除仪表板缓存
    this.localCache.delete('dashboard_stats')

    // 重新获取数据
    return await this.getDashboardStats(true)
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopAutoRefresh()
    this.localCache.clear()
    this.eventListeners.clear()
    this.isInitialized = false
    console.log('🗑️ RealTimeDataService 已销毁')
  }
}

// 创建单例实例
const realTimeDataService = new RealTimeDataService()

export default realTimeDataService
export { RealTimeDataService }
export type { DataResponse, DataItem, UpdateOperation, DashboardStats, RealtimeData }
