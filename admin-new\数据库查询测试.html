<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .result-area {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🔍 数据库查询测试工具</h1>
    
    <div class="test-card">
        <h2>📊 AI模型数据查询</h2>
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="queryAiConfigs()">查询ai_configs集合</button>
            <button class="test-button" onclick="querySystemConfig()">查询system_config集合</button>
            <button class="test-button" onclick="testGetModelsAPI()">测试getModels API</button>
            <button class="test-button" onclick="compareData()">对比数据差异</button>
        </div>

        <div style="margin-bottom: 15px; padding: 15px; background: #fff3cd; border-radius: 6px;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">🔧 AI调用次数修复工具</h4>
            <p style="margin: 0 0 15px 0; color: #856404; font-size: 14px;">
                解决数据大屏显示11次但数据库已清空的问题
            </p>
            <button class="test-button" onclick="debugAICallsStats()" style="background: #ff6b6b; color: white;">🔍 调试AI调用次数</button>
            <button class="test-button" onclick="debugCacheStatus()" style="background: #28a745; color: white;">🗂️ 检查缓存状态</button>
            <button class="test-button" onclick="clearAllCache()" style="background: #dc3545; color: white;">🧹 清理所有缓存</button>
            <button class="test-button" onclick="simpleFixAICalls()" style="background: #6f42c1; color: white;">🔧 简化修复（推荐）</button>
            <button class="test-button" onclick="directCheckAIUsage()" style="background: #e74c3c; color: white;">🔍 直接检查ai_usage集合</button>
            <button class="test-button" onclick="forceDeleteAIUsage()" style="background: #c0392b; color: white;">🔥 强制清空ai_usage集合</button>
        </div>
        <div id="queryResults" class="result-area"></div>
    </div>

    <div class="test-card">
        <h2>📋 数据详情</h2>
        <div id="dataDetails"></div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.19.2/tcb.js"></script>
    <!-- 备用CDN -->
    <script>
        // 检查SDK是否加载成功，如果失败则使用备用方案
        if (typeof tcb === 'undefined') {
            console.warn('主CDN加载失败，尝试备用CDN...');
            const script = document.createElement('script');
            script.src = 'https://imgcache.qq.com/qcloud/tcbjs/1.19.2/tcb.js';
            document.head.appendChild(script);
        }
    </script>

    <script>
        let cloudbaseApp = null;
        let queryResults = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            queryResults += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            document.getElementById('queryResults').innerHTML = queryResults;
            document.getElementById('queryResults').scrollTop = document.getElementById('queryResults').scrollHeight;
        }

        // 等待SDK加载完成
        function waitForSDK() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50; // 5秒超时

                const checkSDK = () => {
                    attempts++;
                    if (typeof tcb !== 'undefined') {
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        reject(new Error('云开发SDK加载超时'));
                    } else {
                        setTimeout(checkSDK, 100);
                    }
                };

                checkSDK();
            });
        }

        async function initCloudbase() {
            try {
                if (cloudbaseApp) return cloudbaseApp;

                // 等待SDK加载
                await waitForSDK();

                log('📦 云开发SDK加载成功，开始初始化...', 'info');

                cloudbaseApp = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                const auth = cloudbaseApp.auth();
                await auth.signInAnonymously();

                log('✅ 云开发SDK初始化成功', 'success');
                return cloudbaseApp;
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                console.error('详细错误信息:', error);

                // 提供备用方案提示
                log('💡 如果持续失败，请尝试以下方案:', 'warning');
                log('   1. 刷新页面重试', 'warning');
                log('   2. 检查网络连接', 'warning');
                log('   3. 使用小程序端的调试工具', 'warning');

                throw error;
            }
        }

        async function queryAiConfigs() {
            log('🔄 开始查询ai_configs集合...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                // 直接查询数据库
                const result = await db.collection('ai_configs').limit(100).get();
                
                log('📊 ai_configs查询结果:', 'success');
                log(`总数量: ${result.data.length}`, 'info');
                
                if (result.data.length > 0) {
                    log('详细数据:', 'info');
                    result.data.forEach((item, index) => {
                        log(`${index + 1}. ID: ${item._id}`, 'info');
                        log(`   名称: ${item.name || '未设置'}`, 'info');
                        log(`   提供商: ${item.provider || '未设置'}`, 'info');
                        log(`   模型: ${item.model || '未设置'}`, 'info');
                        log(`   状态: ${item.status || '未设置'}`, 'info');
                        log(`   创建时间: ${item.createTime || item.createdAt || '未设置'}`, 'info');
                        log('   ---', 'info');
                    });
                    
                    // 显示表格
                    displayDataTable(result.data, 'ai_configs');
                } else {
                    log('❌ 没有找到任何ai_configs数据', 'warning');
                }
                
            } catch (error) {
                log('❌ 查询ai_configs失败: ' + error.message, 'error');
            }
        }

        async function querySystemConfig() {
            log('🔄 开始查询system_config集合...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                const result = await db.collection('system_config').where({
                    type: 'ai_config'
                }).get();
                
                log('📊 system_config查询结果:', 'success');
                log(`AI配置数量: ${result.data.length}`, 'info');
                
                if (result.data.length > 0) {
                    result.data.forEach((item, index) => {
                        log(`${index + 1}. ID: ${item._id}`, 'info');
                        log(`   模型: ${item.model || '未设置'}`, 'info');
                        log(`   提供商: ${item.provider || '未设置'}`, 'info');
                        log(`   状态: ${item.status || '未设置'}`, 'info');
                        log(`   API密钥: ${item.apiKey ? '已设置' : '未设置'}`, 'info');
                        log('   ---', 'info');
                    });
                }
                
            } catch (error) {
                log('❌ 查询system_config失败: ' + error.message, 'error');
            }
        }

        async function testGetModelsAPI() {
            log('🔄 开始测试getModels API...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });

                log('📊 getModels API结果:', 'success');
                
                if (result.result && result.result.code === 200) {
                    const models = result.result.data;
                    log(`API返回模型数量: ${models.length}`, 'info');
                    
                    models.forEach((model, index) => {
                        log(`${index + 1}. ID: ${model.id}`, 'info');
                        log(`   名称: ${model.name}`, 'info');
                        log(`   提供商: ${model.provider}`, 'info');
                        log(`   模型: ${model.model}`, 'info');
                        log(`   状态: ${model.status}`, 'info');
                        log('   ---', 'info');
                    });
                    
                    // 显示API返回的数据表格
                    displayDataTable(models, 'api_result');
                } else {
                    log('❌ API调用失败: ' + JSON.stringify(result.result), 'error');
                }
                
            } catch (error) {
                log('❌ 测试getModels API失败: ' + error.message, 'error');
            }
        }

        async function compareData() {
            log('🔄 开始对比数据差异...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                // 获取数据库原始数据
                const dbResult = await db.collection('ai_configs').limit(100).get();
                
                // 获取API返回数据
                const apiResult = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });
                
                log('📊 数据对比结果:', 'success');
                log(`数据库原始数据: ${dbResult.data.length} 条`, 'info');
                log(`API返回数据: ${apiResult.result?.data?.length || 0} 条`, 'info');
                
                if (dbResult.data.length !== (apiResult.result?.data?.length || 0)) {
                    log('⚠️ 数据数量不一致！', 'warning');
                    
                    // 分析差异
                    const dbIds = dbResult.data.map(item => item._id);
                    const apiIds = apiResult.result?.data?.map(item => item.id) || [];
                    
                    const missingInApi = dbIds.filter(id => !apiIds.includes(id));
                    const extraInApi = apiIds.filter(id => !dbIds.includes(id));
                    
                    if (missingInApi.length > 0) {
                        log(`❌ 数据库中存在但API中缺失的ID: ${missingInApi.join(', ')}`, 'error');
                    }
                    
                    if (extraInApi.length > 0) {
                        log(`➕ API中存在但数据库中没有的ID: ${extraInApi.join(', ')}`, 'warning');
                    }
                } else {
                    log('✅ 数据数量一致', 'success');
                }
                
            } catch (error) {
                log('❌ 对比数据失败: ' + error.message, 'error');
            }
        }

        function displayDataTable(data, type) {
            const detailsDiv = document.getElementById('dataDetails');
            
            if (!data || data.length === 0) {
                detailsDiv.innerHTML = '<p>没有数据可显示</p>';
                return;
            }
            
            let html = `<h3>${type === 'ai_configs' ? '数据库原始数据' : 'API返回数据'}</h3>`;
            html += '<table>';
            html += '<tr><th>ID</th><th>名称</th><th>提供商</th><th>模型</th><th>状态</th><th>创建时间</th></tr>';
            
            data.forEach(item => {
                html += '<tr>';
                html += `<td>${item._id || item.id || '无'}</td>`;
                html += `<td>${item.name || '无'}</td>`;
                html += `<td>${item.provider || '无'}</td>`;
                html += `<td>${item.model || '无'}</td>`;
                html += `<td>${item.status || '无'}</td>`;
                html += `<td>${item.createTime || item.createdAt || '无'}</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            detailsDiv.innerHTML = html;
        }

        // 🔍 调试AI调用次数统计问题
        async function debugAICallsStats() {
            log('🔍 开始调试AI调用次数统计问题...', 'info');

            try {
                const app = await initCloudbase();

                // 调用云函数进行调试
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'debugAICallsStats'
                    }
                });

                log('✅ 调试云函数调用成功', 'success');

                if (result.result && result.result.code === 200) {
                    const debugData = result.result.data;

                    log('📊 调试结果分析:', 'info');
                    log(`⏰ 调试时间: ${debugData.timestamp}`, 'info');

                    // 分析各项检查结果
                    if (debugData.checks && debugData.checks.length > 0) {
                        debugData.checks.forEach(check => {
                            let statusIcon = '📋';
                            let logType = 'info';

                            if (check.status === 'HAS_DATA') {
                                statusIcon = '✅';
                                logType = 'success';
                            } else if (check.status === 'EMPTY') {
                                statusIcon = '⚠️';
                                logType = 'warning';
                            } else if (check.status === 'NOT_EXISTS') {
                                statusIcon = '❌';
                                logType = 'error';
                            }

                            log(`${statusIcon} ${check.name}: ${check.count || 0} 条记录`, logType);

                            if (check.estimatedAICalls) {
                                log(`   📊 估算AI调用: ${check.estimatedAICalls} 次`, 'info');
                            }

                            if (check.sampleData && check.sampleData.length > 0) {
                                log(`   📄 样本数据: ${check.sampleData.length} 条`, 'info');
                            }
                        });
                    }

                    // 重点分析
                    const aiUsageCheck = debugData.checks.find(c => c.name === 'ai_usage集合');
                    const todayCommentsCheck = debugData.checks.find(c => c.name === '今日comments');

                    log('🎯 问题分析结论:', 'info');

                    if (aiUsageCheck && aiUsageCheck.count > 0) {
                        log(`🔥 发现问题源头! ai_usage集合中还有 ${aiUsageCheck.count} 条记录未被清空！`, 'error');
                        log('💡 这很可能就是显示11次的原因。建议清空ai_usage集合。', 'warning');
                    } else if (todayCommentsCheck && todayCommentsCheck.estimatedAICalls > 0) {
                        log(`📊 基于今日评语估算: ${todayCommentsCheck.estimatedAICalls} 次AI调用`, 'info');
                        log('💡 数据来源是基于评语数量的估算，不是真实的AI调用记录。', 'warning');
                    } else {
                        log('🤔 数据库看起来是空的，问题可能来自:', 'warning');
                        log('   1. 管理后台使用了缓存数据', 'warning');
                        log('   2. 小程序本地存储有残留', 'warning');
                        log('   3. 模拟数据被使用', 'warning');
                    }

                    // 显示完整数据
                    log('📄 完整调试数据:', 'info');
                    log(JSON.stringify(debugData, null, 2), 'info');

                } else {
                    log(`❌ 调试失败: ${result.result?.message || '未知错误'}`, 'error');
                }

            } catch (error) {
                log(`❌ 调试过程出错: ${error.message}`, 'error');
                console.error('调试AI调用次数失败:', error);
            }
        }

        // 🗂️ 检查缓存状态
        async function debugCacheStatus() {
            log('🗂️ 检查管理后台缓存状态...', 'info');

            try {
                // 检查localStorage缓存
                const localStorageKeys = Object.keys(localStorage);
                const cacheKeys = localStorageKeys.filter(key =>
                    key.includes('cache') ||
                    key.includes('Cache') ||
                    key.includes('realTime') ||
                    key.includes('dashboard')
                );

                log(`📦 localStorage缓存键: ${cacheKeys.length} 个`, 'info');
                cacheKeys.forEach(key => {
                    try {
                        const data = localStorage.getItem(key);
                        const size = data ? data.length : 0;
                        log(`   - ${key}: ${Math.round(size / 1024)} KB`, 'info');
                    } catch (error) {
                        log(`   - ${key}: 读取失败`, 'error');
                    }
                });

                // 检查特定的缓存数据
                const realTimeCache = localStorage.getItem('realTimeDataCache');
                if (realTimeCache) {
                    try {
                        const cacheData = JSON.parse(realTimeCache);
                        log(`🔍 实时数据缓存: ${cacheData.length} 个项目`, 'info');

                        // 查找仪表板相关缓存
                        const dashboardCache = cacheData.find(([key, value]) => key.includes('dashboard'));
                        if (dashboardCache) {
                            const [key, value] = dashboardCache;
                            log(`📊 发现仪表板缓存:`, 'warning');
                            log(`   键: ${key}`, 'info');
                            log(`   时间: ${new Date(value.timestamp).toLocaleString()}`, 'info');
                            if (value.data && value.data.aiCalls) {
                                log(`   AI调用次数: ${value.data.aiCalls}`, 'warning');
                            }
                            log(`   完整数据: ${JSON.stringify(value.data, null, 2)}`, 'info');
                        }
                    } catch (error) {
                        log(`❌ 解析实时数据缓存失败: ${error.message}`, 'error');
                    }
                }

                // 检查其他可能的缓存
                const lastSyncTime = localStorage.getItem('lastSyncTime');
                if (lastSyncTime) {
                    log(`⏰ 最后同步时间: ${new Date(parseInt(lastSyncTime)).toLocaleString()}`, 'info');
                }

                log('✅ 缓存状态检查完成', 'success');

            } catch (error) {
                log(`❌ 检查缓存状态失败: ${error.message}`, 'error');
                console.error('检查缓存状态失败:', error);
            }
        }

        // 🧹 清理所有缓存
        async function clearAllCache() {
            log('🧹 开始清理所有缓存...', 'info');

            try {
                let clearedCount = 0;
                const errors = [];

                // 清理localStorage中的所有缓存
                const localStorageKeys = Object.keys(localStorage);
                const cacheKeys = localStorageKeys.filter(key =>
                    key.includes('cache') ||
                    key.includes('Cache') ||
                    key.includes('realTime') ||
                    key.includes('dashboard') ||
                    key.includes('sync')
                );

                cacheKeys.forEach(key => {
                    try {
                        localStorage.removeItem(key);
                        clearedCount++;
                        log(`   ✓ 已清理: ${key}`, 'success');
                    } catch (error) {
                        errors.push(`清理 ${key} 失败: ${error.message}`);
                        log(`   ✗ 清理失败: ${key}`, 'error');
                    }
                });

                // 清理sessionStorage
                const sessionKeys = Object.keys(sessionStorage);
                const sessionCacheKeys = sessionKeys.filter(key =>
                    key.includes('cache') ||
                    key.includes('Cache') ||
                    key.includes('dashboard')
                );

                sessionCacheKeys.forEach(key => {
                    try {
                        sessionStorage.removeItem(key);
                        clearedCount++;
                        log(`   ✓ 已清理sessionStorage: ${key}`, 'success');
                    } catch (error) {
                        errors.push(`清理sessionStorage ${key} 失败: ${error.message}`);
                    }
                });

                if (errors.length > 0) {
                    log('⚠️ 清理过程中出现错误:', 'warning');
                    errors.forEach(error => log(`   - ${error}`, 'error'));
                } else {
                    log(`✅ 缓存清理完成，共清理 ${clearedCount} 个项目`, 'success');
                }

                log('💡 建议刷新页面以确保缓存完全清除', 'info');

            } catch (error) {
                log(`❌ 清理缓存失败: ${error.message}`, 'error');
                console.error('清理缓存失败:', error);
            }
        }

        // 🔧 一键修复AI调用次数显示问题
        async function fixAICallsDisplay() {
            log('🔧 开始一键修复AI调用次数显示问题...', 'info');

            try {
                const app = await initCloudbase();

                // 调用修复云函数
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'fixAICallsDisplay'
                    }
                });

                log('✅ 修复云函数调用成功', 'success');

                if (result.result && result.result.success) {
                    const fixData = result.result.data;
                    const recommendations = result.result.recommendations;

                    log('🎯 修复结果总结:', 'info');
                    log(`   发现问题: ${fixData.summary.totalIssuesFound} 个`, 'info');
                    log(`   已修复: ${fixData.summary.totalIssuesFixed} 个`, 'success');
                    log(`   剩余问题: ${fixData.summary.remainingIssues} 个`, fixData.summary.remainingIssues > 0 ? 'warning' : 'success');

                    // 显示修复步骤
                    log('📋 修复步骤详情:', 'info');
                    fixData.steps.forEach((step, index) => {
                        let statusIcon = '📋';
                        let logType = 'info';

                        if (step.status === 'fixed') {
                            statusIcon = '✅';
                            logType = 'success';
                        } else if (step.status === 'ok') {
                            statusIcon = '✓';
                            logType = 'success';
                        } else if (step.status === 'skipped') {
                            statusIcon = '⏭️';
                            logType = 'warning';
                        }

                        log(`   ${statusIcon} 步骤${step.step}: ${step.name}`, logType);
                        if (step.issue && step.issue !== '无') {
                            log(`      问题: ${step.issue}`, 'warning');
                        }
                        if (step.result) {
                            log(`      结果: ${step.result}`, 'info');
                        }
                    });

                    // 显示后续建议
                    log('💡 后续操作建议:', 'info');
                    recommendations.forEach(rec => {
                        log(`   ${rec}`, 'info');
                    });

                    // 自动执行缓存清理
                    log('🧹 自动清理管理后台缓存...', 'info');
                    await clearAllCache();

                    log('🎉 修复完成！建议刷新页面查看效果。', 'success');

                } else {
                    log(`❌ 修复失败: ${result.result?.message || '未知错误'}`, 'error');
                    if (result.result?.data) {
                        log('📄 错误详情:', 'error');
                        log(JSON.stringify(result.result.data, null, 2), 'error');
                    }
                }

            } catch (error) {
                log(`❌ 修复过程出错: ${error.message}`, 'error');
                console.error('一键修复失败:', error);
            }
        }

        // 🔧 简化修复AI调用次数（不依赖云开发SDK）
        async function simpleFixAICalls() {
            log('🔧 开始简化修复AI调用次数显示问题...', 'info');

            try {
                let totalFixed = 0;

                // 步骤1: 清理浏览器缓存
                log('📦 步骤1: 清理浏览器缓存...', 'info');

                const localStorageKeys = Object.keys(localStorage);
                const cacheKeys = localStorageKeys.filter(key =>
                    key.includes('cache') ||
                    key.includes('Cache') ||
                    key.includes('realTime') ||
                    key.includes('dashboard') ||
                    key.includes('sync') ||
                    key.includes('ai') ||
                    key.includes('AI')
                );

                log(`   发现 ${cacheKeys.length} 个相关缓存键`, 'info');

                cacheKeys.forEach(key => {
                    try {
                        localStorage.removeItem(key);
                        totalFixed++;
                        log(`   ✓ 已清理: ${key}`, 'success');
                    } catch (error) {
                        log(`   ✗ 清理失败: ${key}`, 'error');
                    }
                });

                // 清理sessionStorage
                const sessionKeys = Object.keys(sessionStorage);
                const sessionCacheKeys = sessionKeys.filter(key =>
                    key.includes('cache') ||
                    key.includes('dashboard') ||
                    key.includes('ai')
                );

                sessionCacheKeys.forEach(key => {
                    try {
                        sessionStorage.removeItem(key);
                        totalFixed++;
                        log(`   ✓ 已清理sessionStorage: ${key}`, 'success');
                    } catch (error) {
                        log(`   ✗ 清理sessionStorage失败: ${key}`, 'error');
                    }
                });

                // 步骤2: 提供后续指导
                log('📋 步骤2: 后续操作指导', 'info');
                log('   1. 浏览器缓存已清理完成', 'success');
                log('   2. 请打开小程序设置页面', 'warning');
                log('   3. 点击"一键修复AI调用次数"', 'warning');
                log('   4. 然后刷新此管理后台页面', 'warning');

                log(`✅ 简化修复完成！共清理 ${totalFixed} 个缓存项`, 'success');
                log('💡 现在请按照上述步骤完成小程序端修复', 'info');

                // 显示详细说明
                setTimeout(() => {
                    const instructions = `
🔧 AI调用次数修复完整流程

✅ 已完成：浏览器缓存清理 (${totalFixed}个项目)

📱 接下来请在小程序中操作：
1. 打开"评语灵感君"小程序
2. 进入"设置"页面
3. 找到"开发者工具"部分
4. 点击"一键修复AI调用次数"
5. 等待修复完成

🌐 最后在管理后台：
1. 刷新此页面
2. 检查AI调用次数是否恢复正常
3. 如果仍有问题，请重复上述步骤

📞 如需技术支持，请联系开发者并提供修复日志。
                    `;

                    if (confirm('修复指导\n\n' + instructions + '\n\n点击"确定"复制指导内容到剪贴板')) {
                        try {
                            navigator.clipboard.writeText(instructions);
                            log('📋 修复指导已复制到剪贴板', 'success');
                        } catch (error) {
                            log('📋 复制失败，请手动复制上述指导内容', 'warning');
                        }
                    }
                }, 1000);

            } catch (error) {
                log(`❌ 简化修复失败: ${error.message}`, 'error');
                console.error('简化修复失败:', error);
            }
        }

        // 🔍 直接检查ai_usage集合
        async function directCheckAIUsage() {
            log('🔍 直接检查ai_usage集合...', 'info');

            try {
                await waitForSDK();

                const app = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                const auth = app.auth();
                await auth.signInAnonymously();

                log('✅ 云开发SDK初始化成功，开始查询ai_usage集合', 'success');

                // 调用云函数查询ai_usage集合
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'getTotalAICalls'
                    }
                });

                log('📊 ai_usage集合查询结果:', 'info');
                log(`   云函数返回: ${JSON.stringify(result.result, null, 2)}`, 'info');

                if (result.result && result.result.code === 200) {
                    const aiCalls = result.result.data;
                    log(`🎯 AI调用次数: ${aiCalls}`, aiCalls > 0 ? 'error' : 'success');

                    if (aiCalls > 0) {
                        log('🔥 发现问题！ai_usage集合中仍有数据！', 'error');
                        log('💡 建议立即清理ai_usage集合', 'warning');

                        // 提供清理选项
                        if (confirm(`发现ai_usage集合中有${aiCalls}条记录！\n\n这就是显示11次的原因。\n\n是否立即清理？`)) {
                            await clearAIUsageCollection();
                        }
                    } else {
                        log('✅ ai_usage集合是空的，问题可能在其他地方', 'success');
                    }
                } else {
                    log(`❌ 查询失败: ${result.result?.message || '未知错误'}`, 'error');
                }

            } catch (error) {
                log(`❌ 检查ai_usage集合失败: ${error.message}`, 'error');
                console.error('检查ai_usage集合失败:', error);
            }
        }

        // 🧹 清理ai_usage集合
        async function clearAIUsageCollection() {
            log('🧹 开始清理ai_usage集合...', 'warning');

            try {
                const app = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                // 调用修复云函数
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'fixAICallsDisplay'
                    }
                });

                log('🔧 修复云函数调用结果:', 'info');
                log(JSON.stringify(result.result, null, 2), 'info');

                if (result.result && result.result.success) {
                    const fixData = result.result.data;
                    log(`✅ 修复成功！共修复 ${fixData.summary.totalIssuesFixed} 个问题`, 'success');

                    // 显示修复详情
                    fixData.steps.forEach(step => {
                        if (step.status === 'fixed') {
                            log(`   ✓ ${step.name}: ${step.result}`, 'success');
                        }
                    });

                    log('💡 建议现在刷新管理后台页面查看效果', 'info');
                } else {
                    log(`❌ 修复失败: ${result.result?.message || '未知错误'}`, 'error');
                }

            } catch (error) {
                log(`❌ 清理ai_usage集合失败: ${error.message}`, 'error');
                console.error('清理失败:', error);
            }
        }

        // 🔥 强制清空ai_usage集合
        async function forceDeleteAIUsage() {
            if (!confirm('⚠️ 警告！\n\n即将强制清空ai_usage集合中的所有数据！\n\n这个操作不可恢复，确定要继续吗？')) {
                return;
            }

            log('🔥 开始强制清空ai_usage集合...', 'warning');

            try {
                await waitForSDK();

                const app = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                const auth = app.auth();
                await auth.signInAnonymously();

                log('✅ 云开发SDK初始化成功，开始强制删除', 'success');

                // 调用强制删除云函数
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'forceDeleteAIUsage'
                    }
                });

                log('🔥 强制删除云函数调用结果:', 'info');
                log(JSON.stringify(result.result, null, 2), 'info');

                if (result.result && result.result.success) {
                    const deletedCount = result.result.deletedCount;
                    const remainingCount = result.result.remainingCount;

                    log(`✅ 强制删除成功！`, 'success');
                    log(`   删除记录数: ${deletedCount}`, 'success');
                    log(`   剩余记录数: ${remainingCount}`, remainingCount === 0 ? 'success' : 'warning');

                    if (result.result.sampleData && result.result.sampleData.length > 0) {
                        log('📄 已删除的样本数据:', 'info');
                        result.result.sampleData.forEach((item, index) => {
                            log(`   ${index + 1}. ${JSON.stringify(item)}`, 'info');
                        });
                    }

                    if (remainingCount === 0) {
                        log('🎉 ai_usage集合已完全清空！', 'success');
                        log('💡 现在刷新管理后台页面，AI调用次数应该显示为0', 'info');
                    } else {
                        log(`⚠️ 仍有 ${remainingCount} 条记录未删除，可能需要重试`, 'warning');
                    }
                } else {
                    log(`❌ 强制删除失败: ${result.result?.message || '未知错误'}`, 'error');
                    if (result.result?.error) {
                        log(`   错误详情: ${result.result.error}`, 'error');
                    }
                }

            } catch (error) {
                log(`❌ 强制删除过程出错: ${error.message}`, 'error');
                console.error('强制删除失败:', error);
            }
        }

        // 页面加载时自动初始化
        window.onload = function() {
            log('🚀 数据库查询测试工具已启动', 'info');
            log('💡 点击按钮开始查询数据库中的AI模型配置', 'info');
            log('🔧 推荐使用"简化修复"功能解决AI调用次数显示问题', 'warning');
            log('🔍 新增: 点击"直接检查ai_usage集合"可以确认问题源头', 'error');
            log('🔥 新增: 点击"强制清空ai_usage集合"可以彻底解决问题', 'error');
            log('📱 完整修复需要配合小程序端操作', 'info');
        };
    </script>
</body>
</html>
